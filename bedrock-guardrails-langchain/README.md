# Bedrock Guardrails Lang<PERSON>hain Streamlit App

A simple Streamlit application demonstrating AWS Bedrock integration with LangChain and guardrails tracing.

## Features

- 🛡️ AWS Bedrock guardrails integration
- 🔄 Async callback handling for guardrail interventions
- 💬 Interactive chat interface
- 📊 Environment variable configuration with dotenv support
- 🔧 Real-time configuration display

## Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure environment variables:**
   The app uses a `.env` file for configuration. Update the existing `.env` file with your specific values:
   
   ```bash
   # AWS Configuration
   AWS_ACCESS_KEY_ID=your_access_key_here
   AWS_SECRET_ACCESS_KEY=your_secret_key_here
   AWS_REGION=us-east-1
   
   # Bedrock Configuration
   BEDROCK_MODEL_ID=us.amazon.nova-lite-v1:0
   BEDROCK_REGION=us-east-1
   
   # Guardrails Configuration
   GUARDRAIL_IDENTIFIER=gdxoc2c0urug
   GUARDRAIL_VERSION=1
   GUARDRAIL_TRACE=false
   ```

3. **Ensure AWS Bedrock access:**
   Make sure your AWS credentials have access to:
   - Amazon Bedrock service
   - Your configured guardrail
   - The specified model (us.amazon.nova-lite-v1:0)

## Running the App

```bash
streamlit run app.py
```

## Configuration

The app automatically loads configuration from the `.env` file using python-dotenv. You can modify the following settings:

### Guardrails Configuration
- `GUARDRAIL_IDENTIFIER`: Your AWS Bedrock guardrail ID (currently: gdxoc2c0urug)
- `GUARDRAIL_VERSION`: Version of your guardrail (default: 1)
- `GUARDRAIL_TRACE`: Set to 'true' to enable tracing (default: false)

### Model Configuration
- `BEDROCK_MODEL_ID`: The Bedrock model to use (default: us.amazon.nova-lite-v1:0)
- `BEDROCK_REGION`: AWS region for Bedrock (default: us-east-1)

## Callback Handler

The `BedrockAsyncCallbackHandler` class handles:
- Guardrail interventions with user-friendly messages
- LLM start/end events with status updates
- Error handling and user notifications

## Features

- **Environment Variables**: All configuration is managed through environment variables
- **Dotenv Support**: Automatically loads `.env` file on startup
- **Real-time Config Display**: Shows current configuration in the sidebar
- **Chat History**: Maintains conversation history during the session
- **Error Handling**: Graceful handling of guardrail interventions and other errors

## Requirements

- Python 3.8+
- AWS account with Bedrock access
- Configured guardrails in AWS Bedrock
- Valid AWS credentials with appropriate permissions
