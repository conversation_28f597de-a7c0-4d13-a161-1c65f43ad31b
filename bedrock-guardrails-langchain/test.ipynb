{"cells": [{"cell_type": "code", "execution_count": null, "id": "9c04c09f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "id": "ab54aacd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-02 14:28:25,913 - langchain_aws.llms.bedrock - INFO - Using Bedrock Invoke API to generate response\n", "2025-08-02 14:28:26,475 - __main__ - ERROR - Guardrails: {'input': {'5b9vubysaf1x': {'topicPolicy': {'topics': [{'name': 'Microsoft', 'type': 'DENY', 'action': 'BLOCKED', 'detected': True}]}, 'invocationMetrics': {'guardrailProcessingLatency': 330, 'usage': {'topicPolicyUnits': 1, 'contentPolicyUnits': 1, 'wordPolicyUnits': 1, 'sensitiveInformationPolicyUnits': 0, 'sensitiveInformationPolicyFreeUnits': 0, 'contextualGroundingPolicyUnits': 0, 'contentPolicyImageUnits': 0}, 'guardrailCoverage': {'textCharacters': {'guarded': 4, 'total': 4}}}}}, 'actionReason': 'Guardrail blocked.'}\n"]}], "source": ["import logging\n", "from botocore.config import Config\n", "from langchain_aws import ChatBedrock\n", "from langchain.callbacks.base import BaseCallbackHandler\n", "from typing import Any\n", "import streamlit as st\n", "import asyncio\n", "import os\n", "from typing import Any, Mapping\n", "from langchain_aws import ChatBedrock\n", "from langchain.callbacks.base import AsyncCallbackHandler\n", "from langchain.schema import HumanMessage\n", "from dotenv import load_dotenv\n", "\n", "# Setup logging\n", "logging.basicConfig(\n", "    level=logging.INFO, format=\"%(asctime)s - %(name)s - %(levelname)s - %(message)s\"\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "\n", "class GuardrailTraceCallbackHandler(AsyncCallbackHandler):\n", "    async def on_llm_error(self, error: BaseException, **kwargs: Any) -> Any:\n", "        reason = kwargs.get(\"reason\")\n", "        if reason and reason == \"GUARDRAIL_INTERVENED\":\n", "            logger.error(f\"Guardrails: {kwargs.get(\"trace\", {}).get(\"guardrail\", \"\")}\")\n", "\n", "guardrails_config = {\n", "            'guardrailIdentifier': \"5b9vubysaf1x\",\n", "            'guardrailVersion': \"1\",\n", "            \"trace\": \"enabled\"\n", "        }\n", "\n", "guardrail_callback = GuardrailTraceCallbackHandler()\n", "main_llm_id=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\"\n", "\n", "main_llm = ChatBedrock(\n", "    model=main_llm_id,\n", "    guardrails=guardrails_config, \n", "    callbacks = [guardrail_callback]\n", ")\n", "\n", "\n", "async def generate_response(chat_bedrock, user_input: str):\n", "    \"\"\"Generate response using ChatBedrock with async callback handling.\"\"\"\n", "    try:\n", "        # Create a human message\n", "        message = HumanMessage(content=user_input)\n", "        \n", "        # Generate response\n", "        response = await chat_bedrock.agenerate([[message]])\n", "        return response.generations[0][0].text\n", "    except Exception as e:\n", "        st.error(f\"Error generating response: {str(e)}\")\n", "        return None\n", "\n", "prompt = \"msft\"\n", "response = await generate_response(main_llm, prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "450d9fd8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "950359b7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "99c0543f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "775ae300", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}