# Guardrail Alert Banner Implementation

## Overview

This implementation adds an alert banner system that displays the specific reason for guardrail interventions when AWS Bedrock guardrails block content. When the LLM responds with "Sorry, the model cannot answer this question." due to a guardrail intervention, users will now see a detailed alert explaining why the content was blocked.

## Features

### 1. <PERSON>ert Banner Display
- **Visual Alert**: Red error banner with guardrail shield icon (🛡️)
- **Clear Messaging**: Human-readable explanation of why content was blocked
- **Dismissible**: Users can dismiss the alert with a button
- **Technical Details**: Expandable section showing full trace information

### 2. Intelligent Reason Extraction
The system extracts meaningful information from AWS Bedrock guardrail traces and converts them into user-friendly messages:

#### Topic Policy Violations
- **Input**: `{'topicPolicy': {'topics': [{'name': 'Microsoft', 'type': 'DENY', 'action': 'BLOCKED', 'detected': True}]}}`
- **Output**: `"Topic 'Microsoft' is not allowed (DENY policy)"`

#### Content Policy Violations
- **Input**: `{'contentPolicy': {'filters': [{'type': 'HATE_SPEECH', 'action': 'BLOCKED', 'confidence': 'HIGH'}]}}`
- **Output**: `"Content blocked due to HATE_SPEECH (confidence: HIGH)"`

#### Word Policy Violations
- **Input**: `{'wordPolicy': {'words': [{'match': 'badword', 'action': 'BLOCKED'}]}}`
- **Output**: `"Blocked words detected: badword"`

#### Sensitive Information Policy Violations
- **Input**: `{'sensitiveInformationPolicy': {'piiEntities': [{'type': 'EMAIL', 'action': 'BLOCKED'}]}}`
- **Output**: `"Sensitive information detected: EMAIL"`

### 3. Session State Management
- Stores alert information in Streamlit session state
- Automatically clears previous alerts when new requests are made
- Persists alert until user dismisses it

## Implementation Details

### Key Technical Fix

The implementation correctly accesses guardrail trace data using the specific guardrail identifier:
```python
# Correct way to access trace data
if 'input' in trace_info and GUARDRAIL_IDENTIFIER in trace_info['input']:
    details = trace_info['input'][GUARDRAIL_IDENTIFIER]
    # Process the details...
```

Where `GUARDRAIL_IDENTIFIER = "5b9vubysaf1x"` is the specific guardrail ID configured in the application.

### Modified Components

#### 1. GuardrailTraceCallbackHandler Class
```python
class GuardrailTraceCallbackHandler(AsyncCallbackHandler):
    async def on_llm_error(self, error: BaseException, **kwargs: Any) -> Any:
        # Detects guardrail interventions
        # Extracts meaningful reason from trace data using specific guardrail ID
        # Stores alert information in session state
```

#### 2. Alert Display Function
```python
def display_guardrail_alert():
    # Creates error banner with reason
    # Provides expandable technical details
    # Includes dismiss functionality
```

#### 3. Response Generation Enhancement
```python
async def generate_response(chat_bedrock, user_input: str):
    # Clears previous alerts
    # Detects guardrail interventions
    # Triggers UI refresh to show alert
```

### Key Files Modified

1. **app.py**: Main application file with all alert functionality
2. **test_guardrail_alert.py**: Test script to verify reason extraction logic

## Usage Flow

1. **User Input**: User submits a message that triggers guardrails
2. **Guardrail Intervention**: AWS Bedrock blocks the content
3. **Callback Triggered**: `GuardrailTraceCallbackHandler.on_llm_error()` is called
4. **Reason Extraction**: System parses trace data to extract human-readable reason
5. **Session State Update**: Alert information is stored in Streamlit session state
6. **UI Refresh**: Application reruns to display the alert banner
7. **User Notification**: Alert banner appears with specific reason
8. **User Dismissal**: User can dismiss the alert or it clears on next request

## Error Handling

- **Malformed Trace Data**: Falls back to generic "Content blocked by guardrails" message
- **Missing Trace Information**: Provides default message with error indication
- **Multiple Violations**: Combines multiple reasons with semicolon separation
- **Unknown Policy Types**: Gracefully handles unknown guardrail policy types

## Testing

Run the test script to verify reason extraction:
```bash
python test_guardrail_alert.py
```

Expected output:
```
Extracted reason: Topic 'Microsoft' is not allowed (DENY policy)
Content policy reason: Content blocked due to HATE_SPEECH (confidence: HIGH)
Word policy reason: Blocked words detected: badword
```

## Future Enhancements

1. **Styling Improvements**: Custom CSS for better visual design
2. **Logging Integration**: Enhanced logging for guardrail interventions
3. **Analytics**: Track common guardrail triggers for policy refinement
4. **User Feedback**: Allow users to provide feedback on guardrail decisions
5. **Output Guardrails**: Extend support for output guardrail violations
6. **Localization**: Support for multiple languages in alert messages

## Configuration

The alert system works with existing guardrail configuration:
```python
guardrails_config = {
    'guardrailIdentifier': "5b9vubysaf1x",
    'guardrailVersion': "1",
    "trace": "enabled"  # Required for detailed trace information
}
```

**Note**: Ensure `"trace": "enabled"` is set to get detailed guardrail information for meaningful alert messages.
