import streamlit as st
import asyncio
import os
import logging
from typing import Any
from langchain_aws import ChatBedrock
from langchain.callbacks.base import Async<PERSON><PERSON>backHandler
from langchain.schema import HumanMessage
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class GuardrailTraceCallbackHandler(AsyncCallbackHandler):
    """Custom async callback handler for Bedrock guardrails tracing."""

    async def on_llm_error(self, error: BaseException, **kwargs: Any) -> Any:
        global _guardrail_trace
        print(f"🔴 CALLBACK: on_llm_error called")
        reason = kwargs.get("reason")
        if reason and reason == "GUARDRAIL_INTERVENED":
            trace_info = kwargs.get('trace', {}).get('guardrail', '')
            print(trace_info)





def initialize_chatbedrock():
    """Initialize ChatBedrock with guardrails configuration."""
    try:
        # Get configuration from environment variables with fallbacks

        # Configure guardrails parameters (match working Jupyter config)
        guardrails_config = {
            'guardrailIdentifier': "5b9vubysaf1x",
            'guardrailVersion': "1",
            "trace": "enabled"
        }

        # Initialize ChatBedrock with guardrails (match working Jupyter config)
        chat_bedrock = ChatBedrock(
            model='us.anthropic.claude-3-7-sonnet-20250219-v1:0',  # Use same model as working Jupyter
            guardrails=guardrails_config,
            callbacks=[GuardrailTraceCallbackHandler()]
        )
        
        return chat_bedrock
    except Exception as e:
        st.error(f"Failed to initialize ChatBedrock: {str(e)}")
        return None



async def generate_response(chat_bedrock, user_input: str):
    """Generate response using ChatBedrock with async callback handling."""
    try:
        message = HumanMessage(content=user_input)

        response = await chat_bedrock.agenerate([[message]])

        result = response.generations[0][0].text
        return result
    except Exception:
        return None


def main():
    """Main Streamlit application."""
    st.set_page_config(
        page_title="Bedrock Guardrails Chat",
        page_icon="🛡️",
        layout="wide"
    )

    st.title("🛡️ AWS Bedrock Chat with Guardrails")
    st.markdown("This app demonstrates LangChain integration with AWS Bedrock and guardrails tracing.")


    # Initialize ChatBedrock
    if 'chat_bedrock' not in st.session_state:
        with st.spinner("Initializing ChatBedrock..."):
            st.session_state.chat_bedrock = initialize_chatbedrock()
    
    if st.session_state.chat_bedrock is None:
        st.error("Failed to initialize ChatBedrock. Please check your AWS credentials and configuration.")
        st.stop()
    
    # Chat interface
    st.subheader("💬 Chat Interface")
    
    # Initialize chat history
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    # Display chat history
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # Chat input
    if prompt := st.chat_input("Enter your message here..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate and display assistant response
        with st.chat_message("assistant"):
            with st.spinner("Generating response..."):
                try:
                    response = asyncio.run(
                        generate_response(st.session_state.chat_bedrock, prompt)
                    )

                    if response:
                        st.markdown(response)
                        st.session_state.messages.append({"role": "assistant", "content": response})
                    else:
                        st.error("Failed to generate response.")
                except Exception as e:
                    st.error(f"Error: {str(e)}")

    


if __name__ == "__main__":
    main()
